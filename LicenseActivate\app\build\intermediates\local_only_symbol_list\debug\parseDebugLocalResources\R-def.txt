R_DEF: Internal format may change without notice
local
color black
color white
drawable button_background
drawable domain_button_background
drawable edit_text_background
drawable footer_background
drawable gradient_background
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable modern_button_background
drawable modern_edit_text_background
drawable modern_status_background
drawable spinner_background
drawable status_background
id activateButton
id amountField
id amountText
id apiPinField
id autoRechargeCheckbox
id backButton
id checkIntervalField
id countdownText
id dateText
id deviceInfoText
id domainInfoText
id domainLoginButton
id domainLoginSection
id emptyStateLayout
id expirationInfoText
id expirationProgressBar
id expirationSection
id expirationText
id historyRecyclerView
id licenseInfoText
id licenseKeyField
id main
id operatorSpinner
id operatorText
id orderIdText
id phoneNumberField
id phoneNumberText
id pinField
id rechargeButton
id rechargeCard
id rechargeHistoryButton
id rechargeSection
id rechargeSettingsButton
id rechargeStatusText
id refreshButton
id saveSettingsButton
id serverUrlField
id sim1OperatorSpinner
id sim2OperatorSpinner
id simSlotSpinner
id smsResponseText
id startServiceButton
id statusText
id stopServiceButton
id submitRechargeButton
id welcomeText
layout activity_dashboard
layout activity_main
layout activity_recharge
layout activity_recharge_history
layout activity_recharge_settings
layout item_recharge_history
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.Appy99Lisence
style Theme.Appy99Lisence
xml backup_rules
xml data_extraction_rules
xml network_security_config
