package com.mdsadrulhasan.appy99lisence;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.mdsadrulhasan.appy99lisence.recharge.PermissionHelper;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeService;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DashboardActivity extends AppCompatActivity {

    private static final String TAG = "DashboardActivity";

    // Intent extra keys
    public static final String EXTRA_LICENSE_KEY = "license_key";
    public static final String EXTRA_DOMAIN_URL = "domain_url";
    public static final String EXTRA_ACTIVATION_STATUS = "activation_status";
    public static final String EXTRA_EXPIRATION_TIMESTAMP = "expiration_timestamp";
    public static final String EXTRA_DEVICE_ID = "device_id";
    public static final String EXTRA_DEVICE_INFO = "device_info";

    // UI Components
    private TextView welcomeText;
    private TextView licenseInfoText;
    private TextView domainInfoText;
    private TextView expirationInfoText;
    private TextView deviceInfoText;
    private Button backButton;

    // Recharge UI Components
    private LinearLayout rechargeSection;
    private CardView rechargeCard;
    private Button rechargeButton;
    private Button rechargeHistoryButton;
    private Button rechargeSettingsButton;
    private TextView rechargeStatusText;

    // Data from Intent
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    // Recharge functionality
    private SharedPreferences preferences;
    private boolean rechargeModuleEnabled = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_dashboard);

        // Initialize preferences
        preferences = PreferenceManager.getDefaultSharedPreferences(this);

        // Initialize UI components
        initializeUI();

        // Get data from Intent
        extractIntentData();

        // Validate received data
        if (validateData()) {
            // Display dashboard information
            displayDashboardInfo();

            // Initialize recharge functionality
            initializeRechargeModule();
        } else {
            // Handle invalid data
            handleInvalidData();
        }

        Log.d(TAG, "DashboardActivity created");
    }

    /**
     * Initialize UI components
     */
    private void initializeUI() {
        welcomeText = findViewById(R.id.welcomeText);
        licenseInfoText = findViewById(R.id.licenseInfoText);
        domainInfoText = findViewById(R.id.domainInfoText);
        expirationInfoText = findViewById(R.id.expirationInfoText);
        deviceInfoText = findViewById(R.id.deviceInfoText);
        backButton = findViewById(R.id.backButton);

        // Initialize recharge UI components
        rechargeSection = findViewById(R.id.rechargeSection);
        rechargeCard = findViewById(R.id.rechargeCard);
        rechargeButton = findViewById(R.id.rechargeButton);
        rechargeHistoryButton = findViewById(R.id.rechargeHistoryButton);
        rechargeSettingsButton = findViewById(R.id.rechargeSettingsButton);
        rechargeStatusText = findViewById(R.id.rechargeStatusText);

        // Set up back button click listener
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish(); // Close dashboard and return to MainActivity
            }
        });

        // Set up recharge button click listeners
        setupRechargeButtonListeners();
    }

    /**
     * Extract data from Intent extras
     */
    private void extractIntentData() {
        Intent intent = getIntent();

        licenseKey = intent.getStringExtra(EXTRA_LICENSE_KEY);
        domainUrl = intent.getStringExtra(EXTRA_DOMAIN_URL);
        activationStatus = intent.getBooleanExtra(EXTRA_ACTIVATION_STATUS, false);
        expirationTimestamp = intent.getLongExtra(EXTRA_EXPIRATION_TIMESTAMP, 0);
        deviceId = intent.getStringExtra(EXTRA_DEVICE_ID);
        deviceInfo = intent.getStringExtra(EXTRA_DEVICE_INFO);

        Log.d(TAG, "Received data - License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(licenseKey.length(), 8)) + "..." : "null") +
                   ", Domain: " + domainUrl + ", Activated: " + activationStatus);
    }

    /**
     * Validate received data
     */
    private boolean validateData() {
        if (licenseKey == null || licenseKey.isEmpty()) {
            Log.e(TAG, "Invalid license key received");
            return false;
        }

        if (domainUrl == null || domainUrl.isEmpty()) {
            Log.e(TAG, "Invalid domain URL received");
            return false;
        }

        if (!activationStatus) {
            Log.e(TAG, "License not activated");
            return false;
        }

        if (expirationTimestamp <= 0) {
            Log.w(TAG, "Invalid expiration timestamp");
            // Don't fail validation for this, just log warning
        }

        return true;
    }

    /**
     * Display dashboard information
     */
    private void displayDashboardInfo() {
        // Welcome message
        welcomeText.setText("🎉 Welcome to Your Dashboard!");

        // License information
        String maskedLicense = maskLicenseKey(licenseKey);
        licenseInfoText.setText("📄 License: " + maskedLicense);

        // Domain information
        domainInfoText.setText("🌐 Domain: " + domainUrl);

        // Expiration information
        if (expirationTimestamp > 0) {
            String expirationDate = formatTimestamp(expirationTimestamp);
            long timeRemaining = expirationTimestamp - System.currentTimeMillis();

            if (timeRemaining > 0) {
                String timeRemainingStr = formatTimeRemaining(timeRemaining);
                expirationInfoText.setText("⏰ Expires: " + expirationDate + "\n" +
                                         "⏳ Time remaining: " + timeRemainingStr);
            } else {
                expirationInfoText.setText("⚠️ License has expired on: " + expirationDate);
                expirationInfoText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
        } else {
            expirationInfoText.setText("⏰ Expiration: Not available");
        }

        // Device information
        if (deviceInfo != null && !deviceInfo.isEmpty()) {
            deviceInfoText.setText("📱 Device: " + deviceInfo + "\n" +
                                 "🔑 Device ID: " + (deviceId != null ? deviceId.substring(0, Math.min(deviceId.length(), 8)) + "..." : "N/A"));
        } else {
            deviceInfoText.setText("📱 Device information not available");
        }

        Log.d(TAG, "Dashboard information displayed successfully");
    }

    /**
     * Handle invalid data scenario
     */
    private void handleInvalidData() {
        Toast.makeText(this, "❌ Invalid authentication data. Returning to main screen.", Toast.LENGTH_LONG).show();

        // Show error message in UI
        welcomeText.setText("❌ Authentication Error");
        licenseInfoText.setText("Invalid license or authentication data received.");
        domainInfoText.setText("Please return to the main screen and try again.");
        expirationInfoText.setText("");
        deviceInfoText.setText("");

        Log.e(TAG, "Invalid data received, showing error state");

        // Auto-close after a delay
        welcomeText.postDelayed(new Runnable() {
            @Override
            public void run() {
                finish();
            }
        }, 3000); // Close after 3 seconds
    }

    /**
     * Mask license key for security (show only first and last few characters)
     */
    private String maskLicenseKey(String license) {
        if (license == null || license.length() <= 8) {
            return license;
        }

        int start = Math.min(4, license.length() / 3);
        int end = Math.max(license.length() - 4, license.length() * 2 / 3);

        StringBuilder masked = new StringBuilder();
        masked.append(license.substring(0, start));
        for (int i = start; i < end; i++) {
            masked.append("*");
        }
        masked.append(license.substring(end));

        return masked.toString();
    }

    /**
     * Format timestamp to readable date string with 12-hour format
     */
    private String formatTimestamp(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy 'at' hh:mm:ss a", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }

    /**
     * Format time remaining to readable string with seconds
     */
    private String formatTimeRemaining(long timeRemaining) {
        long days = timeRemaining / (24 * 60 * 60 * 1000);
        long hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000);
        long seconds = (timeRemaining % (60 * 1000)) / 1000;

        if (days > 0) {
            return String.format(Locale.getDefault(), "%d days, %d hours, %d minutes, %d seconds", days, hours, minutes, seconds);
        } else if (hours > 0) {
            return String.format(Locale.getDefault(), "%d hours, %d minutes, %d seconds", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format(Locale.getDefault(), "%d minutes, %d seconds", minutes, seconds);
        } else {
            return String.format(Locale.getDefault(), "%d seconds", seconds);
        }
    }

    /**
     * Set up recharge button click listeners
     */
    private void setupRechargeButtonListeners() {
        if (rechargeButton != null) {
            rechargeButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeActivity();
                }
            });
        }

        if (rechargeHistoryButton != null) {
            rechargeHistoryButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeHistoryActivity();
                }
            });
        }

        if (rechargeSettingsButton != null) {
            rechargeSettingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openRechargeSettingsActivity();
                }
            });
        }
    }

    /**
     * Initialize recharge module
     */
    private void initializeRechargeModule() {
        if (!rechargeModuleEnabled) {
            hideRechargeSection();
            return;
        }

        // Save license and device info for recharge module
        saveRechargeCredentials();

        // Show recharge section
        showRechargeSection();

        // Start recharge service if enabled
        if (preferences.getBoolean("auto_start_recharge_service", true)) {
            RechargeService.startRechargeService(this);
        }

        // Update recharge status
        updateRechargeStatus();

        Log.d(TAG, "Recharge module initialized");
    }

    /**
     * Save credentials for recharge module
     */
    private void saveRechargeCredentials() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString("license_key", licenseKey);
        editor.putString("device_id", deviceId);
        editor.putString("domain_url", domainUrl);
        editor.apply();
    }

    /**
     * Show recharge section
     */
    private void showRechargeSection() {
        if (rechargeSection != null) {
            rechargeSection.setVisibility(View.VISIBLE);
        }
        if (rechargeCard != null) {
            rechargeCard.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Hide recharge section
     */
    private void hideRechargeSection() {
        if (rechargeSection != null) {
            rechargeSection.setVisibility(View.GONE);
        }
        if (rechargeCard != null) {
            rechargeCard.setVisibility(View.GONE);
        }
    }

    /**
     * Update recharge status display
     */
    private void updateRechargeStatus() {
        if (rechargeStatusText != null) {
            boolean serviceRunning = preferences.getBoolean("recharge_service_running", false);
            boolean hasPermissions = PermissionHelper.hasAllRechargePermissions(this);

            String status;
            if (!hasPermissions) {
                status = "⚠️ Permissions required for recharge functionality";
            } else if (serviceRunning) {
                status = "🟢 Recharge service is running";
            } else {
                status = "🔴 Recharge service is stopped";
            }

            rechargeStatusText.setText(status);
        }
    }

    /**
     * Open recharge activity
     */
    private void openRechargeActivity() {
        try {
            Intent intent = new Intent(this, RechargeActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            intent.putExtra("domain_url", domainUrl);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge activity", e);
            Toast.makeText(this, "Error opening recharge module", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open recharge history activity
     */
    private void openRechargeHistoryActivity() {
        try {
            Intent intent = new Intent(this, RechargeHistoryActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge history activity", e);
            Toast.makeText(this, "Error opening recharge history", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open recharge settings activity
     */
    private void openRechargeSettingsActivity() {
        try {
            Intent intent = new Intent(this, RechargeSettingsActivity.class);
            intent.putExtra("license_key", licenseKey);
            intent.putExtra("device_id", deviceId);
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening recharge settings activity", e);
            Toast.makeText(this, "Error opening recharge settings", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Update recharge status when returning to dashboard
        if (rechargeModuleEnabled) {
            updateRechargeStatus();
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        Log.d(TAG, "Back button pressed, returning to MainActivity");
    }
}
