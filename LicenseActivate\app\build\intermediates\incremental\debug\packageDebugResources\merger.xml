<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res"><file name="button_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Appy99Lisence</string></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Appy99Lisence" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Appy99Lisence" parent="Base.Theme.Appy99Lisence"/></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Appy99Lisence" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="domain_button_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\domain_button_background.xml" qualifiers="" type="drawable"/><file name="activity_dashboard" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_dashboard.xml" qualifiers="" type="layout"/><file name="footer_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\footer_background.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="modern_button_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\modern_button_background.xml" qualifiers="" type="drawable"/><file name="modern_edit_text_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\modern_edit_text_background.xml" qualifiers="" type="drawable"/><file name="modern_status_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\modern_status_background.xml" qualifiers="" type="drawable"/><file name="activity_recharge" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge.xml" qualifiers="" type="layout"/><file name="spinner_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="activity_recharge_history" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_history.xml" qualifiers="" type="layout"/><file name="activity_recharge_settings" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\activity_recharge_settings.xml" qualifiers="" type="layout"/><file name="item_recharge_history" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\res\layout\item_recharge_history.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>