<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.mdsadrulhasan.appy99lisence"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Network state permission to check connectivity -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Notification permissions for real-time notifications and expiration warnings -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Additional permissions for enhanced notification functionality -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- Recharge module permissions -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Telecom permissions for dual-SIM support -->
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />

    <!-- Background service permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <permission
        android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.Appy99Lisence"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.mdsadrulhasan.appy99lisence.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.mdsadrulhasan.appy99lisence.DashboardActivity"
            android:exported="false"
            android:parentActivityName="com.mdsadrulhasan.appy99lisence.MainActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <!-- Recharge Module Activities -->
        <activity
            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity"
            android:exported="false"
            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>
        <activity
            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity"
            android:exported="false"
            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>
        <activity
            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity"
            android:exported="false"
            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>

        <!-- Recharge Background Service -->
        <service
            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall|dataSync" />

        <!-- SMS Receiver for recharge responses -->
        <receiver
            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSmsReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.mdsadrulhasan.appy99lisence.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>