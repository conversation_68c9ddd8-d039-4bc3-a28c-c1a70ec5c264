package com.appystore.mrecharge.service;


import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.sqlite.SQLiteDatabase;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.Dialfunction;
import java.io.IOException;
import java.io.LineNumberReader;
import java.io.StringReader;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/* loaded from: classes.dex */
public class USSDService extends AccessibilityService {
    public static String TAG = USSDService.class.getSimpleName();
    private SQLiteDatabase database;
    boolean flag;
    AccessibilityNodeInfo inputNode;
    int jo;
    String job;
    String jobb;
    private DbHelper mydb;
    private Dialfunction myser;
    String s;
    SharedPreferences sett;
    SharedPreferences setting;
    AccessibilityNodeInfo source;
    String text;
    Context context = this;
    boolean next = false;
    int jobcount = 1;
    boolean back = false;
    int linr = 1;
    String id = "0";

    @Override // android.accessibilityservice.AccessibilityService
    public void onInterrupt() {
    }

    public void onAccessibilityEvent(android.view.accessibility.AccessibilityEvent r32) {
        /*
            Method dump skipped, instructions count: 1228
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.flexisoftwarebd.serverpro.service.USSDService.onAccessibilityEvent(android.view.accessibility.AccessibilityEvent):void");
    }

    private String testLineExtract(String str, int i, int i2) throws IOException {
        int i3;
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                break;
            }
            if (lineNumberReader.getLineNumber() != i2) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    Log.d("bod", matcher.group());
                    int i4 = 0;
                    try {
                        i3 = Integer.parseInt(matcher.group().toString());
                    } catch (NumberFormatException unused) {
                        i3 = 0;
                    }
                    if (i3 == i) {
                        String str2 = i3 + "tk";
                        String str3 = "tk" + i3;
                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());
                        if (tkfindfinall.equals(str2) || tkfindfinall.equals(str3)) {
                            if (TextUtils.isEmpty(deffind(readLine, "Main", i)) && TextUtils.isEmpty(deffind(readLine, "Default", i))) {
                                String replaceAll = readLine.split("\\s+")[0].replaceAll("\\D+", "");
                                try {
                                    i4 = Integer.parseInt(replaceAll);
                                } catch (NumberFormatException unused2) {
                                }
                                if (i4 == i) {
                                    break;
                                }
                                return replaceAll;
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    private String fetchResponse(AccessibilityNodeInfo accessibilityNodeInfo) {
        if (accessibilityNodeInfo == null) {
            return "";
        }
        String str = "";
        for (int i = 0; i < accessibilityNodeInfo.getChildCount(); i++) {
            AccessibilityNodeInfo child = accessibilityNodeInfo.getChild(i);
            if (child != null) {
                CharSequence text = child.getText();
                if (text != null && child.getClassName().equals(Button.class.getName())) {
                    if (text.toString().toLowerCase().equals("ok") || text.toString().toLowerCase().equals("cancel")) {
                        return str;
                    }
                } else if (text != null && child.getClassName().equals(TextView.class.getName())) {
                    if (text.toString().length() > 10) {
                        str = text.toString();
                    }
                } else if (child.getClassName().equals(ScrollView.class.getName())) {
                    String str2 = str;
                    for (int i2 = 0; i2 < child.getChildCount(); i2++) {
                        AccessibilityNodeInfo child2 = child.getChild(i2);
                        CharSequence text2 = child2.getText();
                        if (text2 != null && child2.getClassName().equals(TextView.class.getName())) {
                            if (text2.toString().length() > 10) {
                                str2 = text2.toString();
                            }
                        } else if (text2 != null && child2.getClassName().equals(Button.class.getName()) && (text2.toString().toLowerCase().equals("ok") || text2.toString().toLowerCase().equals("cancel"))) {
                            return str2;
                        }
                    }
                    str = str2;
                } else {
                    continue;
                }
            }
        }
        return str;
    }

    private String processUSSDText(List<CharSequence> list) {
        Iterator<CharSequence> it = list.iterator();
        if (it.hasNext()) {
            return String.valueOf(it.next());
        }
        return null;
    }

    @Override // android.accessibilityservice.AccessibilityService
    protected void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "onServiceConnected");
        AccessibilityServiceInfo accessibilityServiceInfo = new AccessibilityServiceInfo();
        accessibilityServiceInfo.flags = 1;
        accessibilityServiceInfo.packageNames = new String[]{"com.android.phone"};
        accessibilityServiceInfo.eventTypes = 2080;
        accessibilityServiceInfo.feedbackType = 16;
        setServiceInfo(accessibilityServiceInfo);
    }

    private String morefind(String str, String str2) throws IOException {
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        String str3 = null;
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return str3;
            }
            String capitalize = capitalize(readLine);
            Log.d("good", "" + capitalize);
            if (capitalize.equals("*. back00. next")) {
                str3 = "00";
            } else if (capitalize.indexOf(str2) >= 0) {
                return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
            }
        }
    }

    private String deffind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf(intValue + "tk") >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
            }
        }
    }

    private String rcfind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf("tk " + intValue) >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
            }
        }
    }

    private String capitalize(String str) {
        return (str == null || str.length() == 0) ? "" : str.toLowerCase();
    }

    public boolean haveblank(String str) {
        return TextUtils.isEmpty(str);
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "t");
    }

    public static String getPrefd(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "8888888888888888");
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    public void ClearPreferences(String str) {
        PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit().remove(str).commit();
    }

    private String tkfind(String str) {
        Matcher matcher = Pattern.compile("(tk)(\\d+)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfind2(String str) {
        Matcher matcher = Pattern.compile("(\\d+)(tk)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfindfinall(String str) {
        String tkfind2;
        if (!TextUtils.isEmpty(tkfind(str))) {
            tkfind2 = tkfind(str);
        } else {
            tkfind2 = tkfind2(str);
        }
        return !TextUtils.isEmpty(tkfind2) ? tkfind2.toLowerCase() : "osman";
    }
}