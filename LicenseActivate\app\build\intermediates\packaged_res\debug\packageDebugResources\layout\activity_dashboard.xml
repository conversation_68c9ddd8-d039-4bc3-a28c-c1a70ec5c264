<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5"
    tools:context=".DashboardActivity">

    <!-- Scrollable Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <TextView
                    android:id="@+id/welcomeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎉 Welcome to Your Dashboard!"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#2c3e50"
                    android:layout_marginBottom="8dp"
                    android:gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Your license and domain information"
                    android:textSize="14sp"
                    android:textColor="#7f8c8d"
                    android:gravity="center" />

            </LinearLayout>

            <!-- License Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📄 License Information"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/licenseInfoText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="License: Loading..."
                        android:textSize="14sp"
                        android:textColor="#2c3e50"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Domain Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🌐 Domain Access"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/domainInfoText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Domain: Loading..."
                        android:textSize="14sp"
                        android:textColor="#2c3e50"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Expiration Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ License Expiration"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/expirationInfoText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Expiration: Loading..."
                        android:textSize="14sp"
                        android:textColor="#4CAF50"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Device Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📱 Device Information"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/deviceInfoText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Device: Loading..."
                        android:textSize="14sp"
                        android:textColor="#2c3e50"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Recharge Module Section -->
            <LinearLayout
                android:id="@+id/rechargeSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp"
                android:visibility="visible">

                <!-- Recharge Module Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/rechargeCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:elevation="4dp"
                    android:radius="8dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📱 Mobile Recharge Module"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#34495e"
                            android:layout_marginBottom="12dp" />

                        <TextView
                            android:id="@+id/rechargeStatusText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🔴 Recharge service is stopped"
                            android:textSize="14sp"
                            android:textColor="#2c3e50"
                            android:layout_marginBottom="16dp" />

                        <!-- Recharge Action Buttons -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="3">

                            <Button
                                android:id="@+id/rechargeButton"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:layout_marginEnd="4dp"
                                android:text="💳 Recharge"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="#ffffff"
                                android:background="@drawable/button_background"
                                android:elevation="2dp" />

                            <Button
                                android:id="@+id/rechargeHistoryButton"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:layout_marginStart="4dp"
                                android:layout_marginEnd="4dp"
                                android:text="📊 History"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="#ffffff"
                                android:background="@drawable/button_background"
                                android:elevation="2dp" />

                            <Button
                                android:id="@+id/rechargeSettingsButton"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:layout_marginStart="4dp"
                                android:text="⚙️ Settings"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="#ffffff"
                                android:background="@drawable/button_background"
                                android:elevation="2dp" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- Back Button -->
            <Button
                android:id="@+id/backButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="🔙 Back to Main"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#ffffff"
                android:background="@drawable/button_background"
                android:elevation="2dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Footer Section (Always Visible at Bottom) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#f5f5f5"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎛️ Dashboard - License Management System"
            android:textSize="12sp"
            android:textColor="#7f8c8d"
            android:gravity="center"
            android:padding="8dp" />

    </LinearLayout>

</LinearLayout>
