package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * Database helper for recharge functionality
 * Manages local storage of recharge requests, settings, and SMS logs
 */
public class RechargeDbHelper extends SQLiteOpenHelper {
    
    private static final String TAG = "RechargeDbHelper";
    private static final String DATABASE_NAME = "recharge.db";
    private static final int DATABASE_VERSION = 1;
    
    // Table names
    public static final String TABLE_RECHARGE_LOGS = "recharge_logs";
    public static final String TABLE_RECHARGE_SETTINGS = "recharge_settings";
    public static final String TABLE_SMS_LOGS = "sms_logs";
    public static final String TABLE_OPERATORS = "operators";
    
    // Recharge logs table columns
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_ORDER_ID = "order_id";
    public static final String COLUMN_PHONE_NUMBER = "phone_number";
    public static final String COLUMN_AMOUNT = "amount";
    public static final String COLUMN_OPERATOR = "operator";
    public static final String COLUMN_USSD_CODE = "ussd_code";
    public static final String COLUMN_SIM_SLOT = "sim_slot";
    public static final String COLUMN_STATUS = "status";
    public static final String COLUMN_API_RESPONSE = "api_response";
    public static final String COLUMN_SMS_RESPONSE = "sms_response";
    public static final String COLUMN_CREATED_AT = "created_at";
    public static final String COLUMN_UPDATED_AT = "updated_at";
    
    // Settings table columns
    public static final String COLUMN_LICENSE_KEY = "license_key";
    public static final String COLUMN_DEVICE_ID = "device_id";
    public static final String COLUMN_SIM1_OPERATOR = "sim1_operator";
    public static final String COLUMN_SIM2_OPERATOR = "sim2_operator";
    public static final String COLUMN_AUTO_RECHARGE_ENABLED = "auto_recharge_enabled";
    public static final String COLUMN_SERVER_URL = "server_url";
    public static final String COLUMN_API_PIN = "api_pin";
    public static final String COLUMN_SETTINGS_JSON = "settings_json";
    
    // SMS logs table columns
    public static final String COLUMN_SENDER = "sender";
    public static final String COLUMN_MESSAGE_BODY = "message_body";
    public static final String COLUMN_PROCESSED = "processed";
    
    // Operators table columns
    public static final String COLUMN_NAME = "name";
    public static final String COLUMN_CODE = "code";
    public static final String COLUMN_USSD_PATTERN = "ussd_pattern";
    public static final String COLUMN_COUNTRY = "country";
    
    // Create table statements
    private static final String CREATE_RECHARGE_LOGS_TABLE = 
        "CREATE TABLE " + TABLE_RECHARGE_LOGS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_ORDER_ID + " TEXT UNIQUE NOT NULL, " +
        COLUMN_PHONE_NUMBER + " TEXT NOT NULL, " +
        COLUMN_AMOUNT + " REAL NOT NULL, " +
        COLUMN_OPERATOR + " TEXT NOT NULL, " +
        COLUMN_USSD_CODE + " TEXT, " +
        COLUMN_SIM_SLOT + " INTEGER DEFAULT 1, " +
        COLUMN_STATUS + " TEXT DEFAULT 'pending', " +
        COLUMN_API_RESPONSE + " TEXT, " +
        COLUMN_SMS_RESPONSE + " TEXT, " +
        COLUMN_CREATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP, " +
        COLUMN_UPDATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP" +
        ")";
    
    private static final String CREATE_RECHARGE_SETTINGS_TABLE = 
        "CREATE TABLE " + TABLE_RECHARGE_SETTINGS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_LICENSE_KEY + " TEXT NOT NULL, " +
        COLUMN_DEVICE_ID + " TEXT NOT NULL, " +
        COLUMN_SIM1_OPERATOR + " TEXT, " +
        COLUMN_SIM2_OPERATOR + " TEXT, " +
        COLUMN_AUTO_RECHARGE_ENABLED + " INTEGER DEFAULT 0, " +
        COLUMN_SERVER_URL + " TEXT, " +
        COLUMN_API_PIN + " TEXT, " +
        COLUMN_SETTINGS_JSON + " TEXT, " +
        COLUMN_CREATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP, " +
        COLUMN_UPDATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP, " +
        "UNIQUE(" + COLUMN_LICENSE_KEY + ", " + COLUMN_DEVICE_ID + ")" +
        ")";
    
    private static final String CREATE_SMS_LOGS_TABLE = 
        "CREATE TABLE " + TABLE_SMS_LOGS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_DEVICE_ID + " TEXT, " +
        COLUMN_SENDER + " TEXT NOT NULL, " +
        COLUMN_MESSAGE_BODY + " TEXT NOT NULL, " +
        COLUMN_ORDER_ID + " TEXT, " +
        COLUMN_PROCESSED + " INTEGER DEFAULT 0, " +
        COLUMN_CREATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP" +
        ")";
    
    private static final String CREATE_OPERATORS_TABLE = 
        "CREATE TABLE " + TABLE_OPERATORS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_NAME + " TEXT NOT NULL, " +
        COLUMN_CODE + " TEXT UNIQUE NOT NULL, " +
        COLUMN_USSD_PATTERN + " TEXT NOT NULL, " +
        COLUMN_COUNTRY + " TEXT DEFAULT 'BD', " +
        COLUMN_CREATED_AT + " DATETIME DEFAULT CURRENT_TIMESTAMP" +
        ")";
    
    public RechargeDbHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        try {
            db.execSQL(CREATE_RECHARGE_LOGS_TABLE);
            db.execSQL(CREATE_RECHARGE_SETTINGS_TABLE);
            db.execSQL(CREATE_SMS_LOGS_TABLE);
            db.execSQL(CREATE_OPERATORS_TABLE);
            
            // Insert default operators
            insertDefaultOperators(db);
            
            Log.d(TAG, "Recharge database tables created successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error creating recharge database tables", e);
        }
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // Drop existing tables
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_RECHARGE_LOGS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_RECHARGE_SETTINGS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SMS_LOGS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_OPERATORS);
        
        // Recreate tables
        onCreate(db);
    }
    
    private void insertDefaultOperators(SQLiteDatabase db) {
        String[][] operators = {
            {"Grameenphone", "GP", "*121*{amount}*{number}#", "BD"},
            {"Robi", "ROBI", "*123*{amount}*{number}#", "BD"},
            {"Banglalink", "BL", "*124*{amount}*{number}#", "BD"},
            {"Airtel", "AIRTEL", "*125*{amount}*{number}#", "BD"},
            {"Teletalk", "TT", "*126*{amount}*{number}#", "BD"}
        };
        
        for (String[] operator : operators) {
            ContentValues values = new ContentValues();
            values.put(COLUMN_NAME, operator[0]);
            values.put(COLUMN_CODE, operator[1]);
            values.put(COLUMN_USSD_PATTERN, operator[2]);
            values.put(COLUMN_COUNTRY, operator[3]);
            
            db.insertWithOnConflict(TABLE_OPERATORS, null, values, SQLiteDatabase.CONFLICT_IGNORE);
        }
    }
    
    // Recharge logs methods
    public long insertRechargeLog(String orderId, String phoneNumber, double amount, 
                                  String operator, String ussdCode, int simSlot) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_ORDER_ID, orderId);
        values.put(COLUMN_PHONE_NUMBER, phoneNumber);
        values.put(COLUMN_AMOUNT, amount);
        values.put(COLUMN_OPERATOR, operator);
        values.put(COLUMN_USSD_CODE, ussdCode);
        values.put(COLUMN_SIM_SLOT, simSlot);
        values.put(COLUMN_STATUS, "pending");
        
        long result = db.insert(TABLE_RECHARGE_LOGS, null, values);
        db.close();
        
        Log.d(TAG, "Inserted recharge log with ID: " + result);
        return result;
    }
    
    public boolean updateRechargeStatus(String orderId, String status, String apiResponse, String smsResponse) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_STATUS, status);
        if (apiResponse != null) values.put(COLUMN_API_RESPONSE, apiResponse);
        if (smsResponse != null) values.put(COLUMN_SMS_RESPONSE, smsResponse);
        values.put(COLUMN_UPDATED_AT, System.currentTimeMillis());
        
        int rowsAffected = db.update(TABLE_RECHARGE_LOGS, values, 
                                   COLUMN_ORDER_ID + " = ?", new String[]{orderId});
        db.close();
        
        Log.d(TAG, "Updated recharge status for order: " + orderId + ", rows affected: " + rowsAffected);
        return rowsAffected > 0;
    }
    
    public Cursor getPendingRecharges() {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_RECHARGE_LOGS, null, 
                       COLUMN_STATUS + " = ?", new String[]{"pending"}, 
                       null, null, COLUMN_CREATED_AT + " ASC");
    }
    
    public Cursor getRechargeHistory(int limit) {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_RECHARGE_LOGS, null, null, null, 
                       null, null, COLUMN_CREATED_AT + " DESC", String.valueOf(limit));
    }
    
    public Cursor getRechargeByOrderId(String orderId) {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_RECHARGE_LOGS, null, 
                       COLUMN_ORDER_ID + " = ?", new String[]{orderId}, 
                       null, null, null);
    }
    
    // Settings methods
    public boolean saveRechargeSettings(String licenseKey, String deviceId, String sim1Operator, 
                                       String sim2Operator, boolean autoRechargeEnabled, 
                                       String serverUrl, String apiPin, String settingsJson) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_LICENSE_KEY, licenseKey);
        values.put(COLUMN_DEVICE_ID, deviceId);
        values.put(COLUMN_SIM1_OPERATOR, sim1Operator);
        values.put(COLUMN_SIM2_OPERATOR, sim2Operator);
        values.put(COLUMN_AUTO_RECHARGE_ENABLED, autoRechargeEnabled ? 1 : 0);
        values.put(COLUMN_SERVER_URL, serverUrl);
        values.put(COLUMN_API_PIN, apiPin);
        values.put(COLUMN_SETTINGS_JSON, settingsJson);
        values.put(COLUMN_UPDATED_AT, System.currentTimeMillis());
        
        long result = db.insertWithOnConflict(TABLE_RECHARGE_SETTINGS, null, values, 
                                            SQLiteDatabase.CONFLICT_REPLACE);
        db.close();
        
        Log.d(TAG, "Saved recharge settings, result: " + result);
        return result != -1;
    }
    
    public Cursor getRechargeSettings(String licenseKey, String deviceId) {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_RECHARGE_SETTINGS, null, 
                       COLUMN_LICENSE_KEY + " = ? AND " + COLUMN_DEVICE_ID + " = ?", 
                       new String[]{licenseKey, deviceId}, null, null, null);
    }
    
    // SMS logs methods
    public long insertSmsLog(String deviceId, String sender, String messageBody, String orderId) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_DEVICE_ID, deviceId);
        values.put(COLUMN_SENDER, sender);
        values.put(COLUMN_MESSAGE_BODY, messageBody);
        values.put(COLUMN_ORDER_ID, orderId);
        values.put(COLUMN_PROCESSED, 0);
        
        long result = db.insert(TABLE_SMS_LOGS, null, values);
        db.close();
        
        Log.d(TAG, "Inserted SMS log with ID: " + result);
        return result;
    }
    
    public Cursor getUnprocessedSms() {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_SMS_LOGS, null, 
                       COLUMN_PROCESSED + " = ?", new String[]{"0"}, 
                       null, null, COLUMN_CREATED_AT + " ASC");
    }
    
    public boolean markSmsAsProcessed(long smsId) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(COLUMN_PROCESSED, 1);
        
        int rowsAffected = db.update(TABLE_SMS_LOGS, values, 
                                   COLUMN_ID + " = ?", new String[]{String.valueOf(smsId)});
        db.close();
        
        return rowsAffected > 0;
    }
    
    // Operators methods
    public Cursor getAllOperators() {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_OPERATORS, null, null, null, 
                       null, null, COLUMN_NAME + " ASC");
    }
    
    public String getUssdPattern(String operatorCode) {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_OPERATORS, new String[]{COLUMN_USSD_PATTERN}, 
                               COLUMN_CODE + " = ?", new String[]{operatorCode}, 
                               null, null, null);
        
        String pattern = null;
        if (cursor.moveToFirst()) {
            pattern = cursor.getString(0);
        }
        cursor.close();
        db.close();
        
        return pattern;
    }
    
    // Utility methods
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_RECHARGE_LOGS, null, null);
        db.delete(TABLE_SMS_LOGS, null, null);
        // Keep settings and operators
        db.close();
        
        Log.d(TAG, "Cleared all recharge data");
    }
    
    public int getRechargeCount(String status) {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_RECHARGE_LOGS, new String[]{"COUNT(*)"}, 
                               status != null ? COLUMN_STATUS + " = ?" : null, 
                               status != null ? new String[]{status} : null, 
                               null, null, null);
        
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        db.close();
        
        return count;
    }
}
