1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdsadrulhasan.appy99lisence"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Network state permission to check connectivity -->
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Notification permissions for real-time notifications and expiration warnings -->
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:22-63
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:5-68
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:22-65
21
22    <!-- Additional permissions for enhanced notification functionality -->
23    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:5-81
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:5-77
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:22-74
25
26    <!-- Recharge module permissions -->
27    <uses-permission android:name="android.permission.CALL_PHONE" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:5-69
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:22-66
28    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:5-75
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:22-72
29    <uses-permission android:name="android.permission.RECEIVE_SMS" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:5-70
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:22-67
30    <uses-permission android:name="android.permission.READ_SMS" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:5-67
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:22-64
31    <uses-permission android:name="android.permission.SEND_SMS" />
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:5-67
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:22-64
32    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
32-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:26:5-78
32-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:26:22-75
33    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
33-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:27:5-95
33-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:27:22-92
34
35    <!-- Telecom permissions for dual-SIM support -->
36    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:30:5-74
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:30:22-71
37    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
37-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:31:5-77
37-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:31:22-74
38
39    <!-- Background service permissions -->
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:34:5-88
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:34:22-85
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:35:5-87
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:35:22-84
42
43    <permission
43-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
44        android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:37:5-113:19
50        android:allowBackup="true"
50-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:38:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
52        android:dataExtractionRules="@xml/data_extraction_rules"
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:39:9-65
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:fullBackupContent="@xml/backup_rules"
55-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:40:9-54
56        android:icon="@mipmap/ic_launcher"
56-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41:9-43
57        android:label="@string/app_name"
57-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42:9-41
58        android:networkSecurityConfig="@xml/network_security_config"
58-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:46:9-69
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:43:9-54
60        android:supportsRtl="true"
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:44:9-35
61        android:testOnly="true"
62        android:theme="@style/Theme.Appy99Lisence"
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:45:9-51
63        android:usesCleartextTraffic="true" >
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:47:9-44
64        <activity
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:49:9-57:20
65            android:name="com.mdsadrulhasan.appy99lisence.MainActivity"
65-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:50:13-41
66            android:exported="true" >
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:51:13-36
67            <intent-filter>
67-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:52:13-56:29
68                <action android:name="android.intent.action.MAIN" />
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:53:17-69
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:53:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:55:17-77
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:55:27-74
71            </intent-filter>
72        </activity>
73        <activity
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:59:9-66:20
74            android:name="com.mdsadrulhasan.appy99lisence.DashboardActivity"
74-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:60:13-46
75            android:exported="false"
75-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:61:13-37
76            android:parentActivityName="com.mdsadrulhasan.appy99lisence.MainActivity" >
76-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:62:13-55
77            <meta-data
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-65:49
78                android:name="android.support.PARENT_ACTIVITY"
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-63
79                android:value=".MainActivity" />
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:65:17-46
80        </activity>
81
82        <!-- Recharge Module Activities -->
83        <activity
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:69:9-76:20
84            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity"
84-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:70:13-54
85            android:exported="false"
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:71:13-37
86            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:72:13-60
87            <meta-data
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-65:49
88                android:name="android.support.PARENT_ACTIVITY"
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-63
89                android:value=".DashboardActivity" />
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:65:17-46
90        </activity>
91        <activity
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:78:9-85:20
92            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity"
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:79:13-61
93            android:exported="false"
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:80:13-37
94            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:81:13-60
95            <meta-data
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-65:49
96                android:name="android.support.PARENT_ACTIVITY"
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-63
97                android:value=".DashboardActivity" />
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:65:17-46
98        </activity>
99        <activity
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:87:9-94:20
100            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity"
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:88:13-62
101            android:exported="false"
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:89:13-37
102            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:90:13-60
103            <meta-data
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-65:49
104                android:name="android.support.PARENT_ACTIVITY"
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-63
105                android:value=".DashboardActivity" />
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:65:17-46
106        </activity>
107
108        <!-- Recharge Background Service -->
109        <service
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:97:9-101:66
110            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeService"
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:98:13-53
111            android:enabled="true"
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:99:13-35
112            android:exported="false"
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:100:13-37
113            android:foregroundServiceType="phoneCall|dataSync" />
113-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:101:13-63
114
115        <!-- SMS Receiver for recharge responses -->
116        <receiver
116-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:104:9-111:20
117            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSmsReceiver"
117-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:105:13-57
118            android:enabled="true"
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:106:13-35
119            android:exported="true" >
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:107:13-36
120            <intent-filter android:priority="1000" >
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:108:13-110:29
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:108:28-51
121                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:109:17-82
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:109:25-79
122            </intent-filter>
123        </receiver>
124
125        <provider
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
126            android:name="androidx.startup.InitializationProvider"
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
127            android:authorities="com.mdsadrulhasan.appy99lisence.androidx-startup"
127-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
128            android:exported="false" >
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
129            <meta-data
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.emoji2.text.EmojiCompatInitializer"
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
131                android:value="androidx.startup" />
131-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <uses-library
140-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
141            android:name="androidx.window.extensions"
141-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
142            android:required="false" />
142-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
143        <uses-library
143-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
144            android:name="androidx.window.sidecar"
144-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
145            android:required="false" />
145-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
146
147        <receiver
147-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
148            android:name="androidx.profileinstaller.ProfileInstallReceiver"
148-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
149            android:directBootAware="false"
149-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
150            android:enabled="true"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
151            android:exported="true"
151-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
152            android:permission="android.permission.DUMP" >
152-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
154                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
154-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
154-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
157                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
160                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
163                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
164            </intent-filter>
165        </receiver>
166    </application>
167
168</manifest>
